import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Product from '@/lib/models/Product';
import CustomerDebt from '@/lib/models/CustomerDebt';

// GET /api/inventory/insights - Get inventory insights and recommendations
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // 1. Stock Level Analysis
    const stockAnalysis = await Product.aggregate([
      {
        $addFields: {
          stockStatus: {
            $switch: {
              branches: [
                { case: { $eq: ['$stockQuantity', 0] }, then: 'out_of_stock' },
                { case: { $lte: ['$stockQuantity', 2] }, then: 'critical' },
                { case: { $lte: ['$stockQuantity', 5] }, then: 'low' },
                { case: { $lte: ['$stockQuantity', 10] }, then: 'moderate' },
              ],
              default: 'good'
            }
          }
        }
      },
      {
        $group: {
          _id: '$stockStatus',
          count: { $sum: 1 },
          products: { $push: '$$ROOT' }
        }
      }
    ]);

    // 2. Sales Velocity Analysis (based on debt records as sales proxy)
    const salesVelocity = await CustomerDebt.aggregate([
      {
        $match: {
          dateOfDebt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
        }
      },
      {
        $group: {
          _id: '$productName',
          totalSold: { $sum: '$quantity' },
          totalRevenue: { $sum: '$totalAmount' },
          averagePrice: { $avg: '$productPrice' },
          salesCount: { $sum: 1 },
          lastSaleDate: { $max: '$dateOfDebt' }
        }
      },
      {
        $addFields: {
          dailyVelocity: { $divide: ['$totalSold', 30] },
          revenuePerDay: { $divide: ['$totalRevenue', 30] }
        }
      },
      { $sort: { dailyVelocity: -1 } }
    ]);

    // 3. Reorder Recommendations
    const products = await Product.find({}).lean();
    const reorderRecommendations = [];

    for (const product of products) {
      const salesData = salesVelocity.find(s => s._id === product.name);
      const dailyVelocity = salesData?.dailyVelocity || 0;
      
      // Calculate days until stock out
      const daysUntilStockOut = dailyVelocity > 0 ? product.stockQuantity / dailyVelocity : Infinity;
      
      // Recommend reorder if stock will run out in less than 7 days
      if (daysUntilStockOut < 7 && daysUntilStockOut !== Infinity) {
        const recommendedOrderQuantity = Math.ceil(dailyVelocity * 14); // 2 weeks supply
        
        reorderRecommendations.push({
          productId: product._id,
          productName: product.name,
          currentStock: product.stockQuantity,
          dailyVelocity: Math.round(dailyVelocity * 100) / 100,
          daysUntilStockOut: Math.round(daysUntilStockOut * 10) / 10,
          recommendedOrderQuantity,
          estimatedCost: recommendedOrderQuantity * product.price,
          priority: daysUntilStockOut < 3 ? 'urgent' : daysUntilStockOut < 5 ? 'high' : 'medium',
          category: product.category,
          price: product.price
        });
      }
    }

    // Sort recommendations by priority and days until stock out
    reorderRecommendations.sort((a, b) => {
      const priorityOrder: { [key: string]: number } = { urgent: 3, high: 2, medium: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return a.daysUntilStockOut - b.daysUntilStockOut;
    });

    // 4. Category Performance Analysis
    const categoryPerformance = await Product.aggregate([
      {
        $lookup: {
          from: 'customerdebts',
          localField: 'name',
          foreignField: 'productName',
          as: 'sales'
        }
      },
      {
        $addFields: {
          totalSold: {
            $sum: {
              $map: {
                input: '$sales',
                as: 'sale',
                in: '$$sale.quantity'
              }
            }
          },
          totalRevenue: {
            $sum: {
              $map: {
                input: '$sales',
                as: 'sale',
                in: '$$sale.totalAmount'
              }
            }
          }
        }
      },
      {
        $group: {
          _id: '$category',
          productCount: { $sum: 1 },
          totalStockValue: { $sum: { $multiply: ['$price', '$stockQuantity'] } },
          totalSold: { $sum: '$totalSold' },
          totalRevenue: { $sum: '$totalRevenue' },
          averagePrice: { $avg: '$price' },
          lowStockCount: {
            $sum: { $cond: [{ $lte: ['$stockQuantity', 5] }, 1, 0] }
          },
          outOfStockCount: {
            $sum: { $cond: [{ $eq: ['$stockQuantity', 0] }, 1, 0] }
          }
        }
      },
      {
        $addFields: {
          turnoverRate: {
            $cond: [
              { $gt: ['$totalStockValue', 0] },
              { $divide: ['$totalRevenue', '$totalStockValue'] },
              0
            ]
          }
        }
      },
      { $sort: { totalRevenue: -1 } }
    ]);

    // 5. Slow Moving Items (products with low sales velocity)
    const slowMovingItems = products
      .map(product => {
        const salesData = salesVelocity.find(s => s._id === product.name);
        const dailyVelocity = salesData?.dailyVelocity || 0;
        const daysSinceLastSale = salesData?.lastSaleDate
          ? Math.floor((Date.now() - new Date(salesData.lastSaleDate).getTime()) / (1000 * 60 * 60 * 24))
          : 999;

        return {
          _id: product._id,
          name: product.name,
          price: product.price,
          stockQuantity: product.stockQuantity,
          category: product.category,
          dailyVelocity,
          daysSinceLastSale,
          stockValue: product.price * product.stockQuantity
        };
      })
      .filter(product =>
        product.dailyVelocity < 0.1 && // Less than 0.1 units per day
        product.daysSinceLastSale > 14 && // No sales in 2 weeks
        product.stockQuantity > 0 // Has stock
      )
      .sort((a, b) => b.stockValue - a.stockValue) // Sort by stock value (highest first)
      .slice(0, 10);

    // 6. Fast Moving Items (high velocity products)
    const fastMovingItems = salesVelocity
      .filter(item => item.dailyVelocity > 1) // More than 1 unit per day
      .slice(0, 10)
      .map(item => {
        const product = products.find(p => p.name === item._id);
        return {
          ...item,
          currentStock: product?.stockQuantity || 0,
          stockValue: product ? product.price * product.stockQuantity : 0,
          category: product?.category || 'unknown'
        };
      });

    // 7. Inventory Health Score
    const totalProducts = products.length;
    const outOfStockCount = stockAnalysis.find(s => s._id === 'out_of_stock')?.count || 0;
    const lowStockCount = (stockAnalysis.find(s => s._id === 'critical')?.count || 0) + 
                         (stockAnalysis.find(s => s._id === 'low')?.count || 0);
    
    const healthScore = Math.round(((totalProducts - outOfStockCount - lowStockCount) / totalProducts) * 100);

    const insights = {
      stockAnalysis: stockAnalysis.reduce((acc, item) => {
        acc[item._id] = {
          count: item.count,
          products: item.products.slice(0, 5) // Limit to 5 products per status
        };
        return acc;
      }, {}),
      reorderRecommendations,
      categoryPerformance,
      slowMovingItems,
      fastMovingItems,
      inventoryHealthScore: healthScore,
      summary: {
        totalProducts,
        outOfStockCount,
        lowStockCount,
        reorderNeeded: reorderRecommendations.length,
        slowMovingCount: slowMovingItems.length,
        totalInventoryValue: products.reduce((sum, p) => sum + (p.price * p.stockQuantity), 0)
      },
      generatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: insights
    });

  } catch (error) {
    console.error('Error fetching inventory insights:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch inventory insights',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
