(()=>{var e={};e.id=745,e.ids=[745],e.modules={180:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(5239),a=t(8088),l=t(8170),i=t.n(l),d=t(893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c={children:["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1031)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\analytics\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},621:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(687),a=t(3210),l=t(2254),i=t(2730),d=t(8122),n=t(3928),c=t(9947),o=t(9080);let x=(0,t(8962).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var m=t(5541),h=t(4808),p=t(228),u=t(1312);function g(){let[e,s]=(0,a.useState)(null),[t,g]=(0,a.useState)(!0),[j,v]=(0,a.useState)("30"),y=async()=>{g(!0);try{let e=await fetch(`/api/analytics?period=${j}`),t=await e.json();t.success&&s(t.data)}catch(e){}finally{g(!1)}},b=e=>{switch(e){case"high":return"text-red-600 bg-red-50 border-red-200";case"medium":return"text-orange-600 bg-orange-50 border-orange-200";default:return"text-yellow-600 bg-yellow-50 border-yellow-200"}};return t?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,r.jsx)("span",{className:"text-lg text-gray-600",children:"Loading analytics..."})]})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(l.A,{}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Business Analytics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Advanced insights and performance metrics"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-4 sm:mt-0",children:[(0,r.jsxs)("select",{value:j,onChange:e=>v(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"7",children:"Last 7 days"}),(0,r.jsx)("option",{value:"30",children:"Last 30 days"}),(0,r.jsx)("option",{value:"90",children:"Last 90 days"}),(0,r.jsx)("option",{value:"365",children:"Last year"})]}),(0,r.jsxs)("button",{onClick:y,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Refresh"})]})]})]}),e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 text-green-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.totalRevenue.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-red-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Amount"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.totalUnpaidAmount.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Inventory Value"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.totalInventoryValue.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x,{className:"h-8 w-8 text-purple-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Debt"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.averageDebtAmount.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-orange-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Payment Rate"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.businessSummary.paymentRate.toFixed(1),"%"]})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(h.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Inventory by Category"})]}),(0,r.jsx)("div",{className:"space-y-4",children:e.inventoryAnalytics.map(e=>(0,r.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 capitalize",children:e.category}),(0,r.jsxs)("span",{className:"text-sm font-bold text-blue-600",children:["₱",e.totalStockValue.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:["Products: ",e.totalProducts]}),(0,r.jsxs)("div",{children:["Stock: ",e.totalStock]}),(0,r.jsxs)("div",{children:["Avg Price: ₱",e.averagePrice.toFixed(2)]}),(0,r.jsxs)("div",{className:"text-red-600",children:["Low Stock: ",e.lowStockCount]})]})]},e.category))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(m.A,{className:"h-6 w-6 text-green-600 mr-2"}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Top Products"})]}),(0,r.jsx)("div",{className:"space-y-3",children:e.productPerformance.slice(0,5).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3",children:s+1}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.productName}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.totalSold," sold • ",e.uniqueCustomers," customers"]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-bold text-green-600",children:["₱",e.totalRevenue.toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₱",e.averagePrice.toFixed(2)," avg"]})]})]},e.productName))})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(p.A,{className:"h-6 w-6 text-red-600 mr-2"}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Overdue Debts"})]}),e.overdueDebts.length>0?(0,r.jsx)("div",{className:"space-y-3",children:e.overdueDebts.slice(0,5).map(e=>(0,r.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.customerName}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.productName})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-bold text-red-600",children:["₱",e.totalAmount.toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm text-red-500",children:[e.daysOverdue," days overdue"]})]})]})},`${e.customerName}-${e.productName}`))}):(0,r.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No overdue debts"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(u.A,{className:"h-6 w-6 text-orange-600 mr-2"}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Risk Customers"})]}),e.riskCustomers.length>0?(0,r.jsx)("div",{className:"space-y-3",children:e.riskCustomers.slice(0,5).map(e=>(0,r.jsxs)("div",{className:`p-3 border rounded-lg ${b(e.riskLevel)}`,children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("p",{className:"font-medium",children:e.customerName}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-bold",children:["₱",e.unpaidAmount.toFixed(2)]}),(0,r.jsxs)("span",{className:`text-xs px-2 py-1 rounded-full ${"high"===e.riskLevel?"bg-red-100 text-red-800":"medium"===e.riskLevel?"bg-orange-100 text-orange-800":"bg-yellow-100 text-yellow-800"}`,children:[e.riskLevel.toUpperCase()," RISK"]})]})]}),(0,r.jsx)("div",{className:"text-sm",children:e.riskFactors.map((e,s)=>(0,r.jsxs)("span",{className:"inline-block mr-2 mb-1",children:["• ",e]},s))})]},e.customerName))}):(0,r.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No high-risk customers"})]})]})]})]})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1031:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\analytics\\page.tsx","default")},2730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2856:(e,s,t)=>{Promise.resolve().then(t.bind(t,621))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},5541:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},8122:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9304:(e,s,t)=>{Promise.resolve().then(t.bind(t,1031))},9947:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(8962).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,945,73,188],()=>t(180));module.exports=r})();