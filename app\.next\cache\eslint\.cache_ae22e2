[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\types.ts": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\admin\\page.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\analytics\\page.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\admin\\seed\\route.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\analytics\\route.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\inventory\\insights\\route.ts": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\notifications\\route.ts": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\notifications\\page.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\NotificationCenter.tsx": "28"}, {"size": 3219, "mtime": 1750602727908, "results": "29", "hashOfConfig": "30"}, {"size": 2865, "mtime": 1750602709015, "results": "31", "hashOfConfig": "30"}, {"size": 3095, "mtime": 1750602754833, "results": "32", "hashOfConfig": "30"}, {"size": 3902, "mtime": 1750591102920, "results": "33", "hashOfConfig": "30"}, {"size": 2704, "mtime": 1750602688986, "results": "34", "hashOfConfig": "30"}, {"size": 4026, "mtime": 1750591103175, "results": "35", "hashOfConfig": "30"}, {"size": 14248, "mtime": 1750604096687, "results": "36", "hashOfConfig": "30"}, {"size": 618, "mtime": 1750591135161, "results": "37", "hashOfConfig": "30"}, {"size": 14774, "mtime": 1750670769458, "results": "38", "hashOfConfig": "30"}, {"size": 8152, "mtime": 1750604070378, "results": "39", "hashOfConfig": "30"}, {"size": 7998, "mtime": 1750603985508, "results": "40", "hashOfConfig": "30"}, {"size": 5413, "mtime": 1750603893162, "results": "41", "hashOfConfig": "30"}, {"size": 11200, "mtime": 1750603960281, "results": "42", "hashOfConfig": "30"}, {"size": 2480, "mtime": 1750670608248, "results": "43", "hashOfConfig": "30"}, {"size": 3617, "mtime": 1750603795936, "results": "44", "hashOfConfig": "30"}, {"size": 9655, "mtime": 1750603936356, "results": "45", "hashOfConfig": "30"}, {"size": 3520, "mtime": 1750604206697, "results": "46", "hashOfConfig": "30"}, {"size": 2257, "mtime": 1750604227433, "results": "47", "hashOfConfig": "30"}, {"size": 2090, "mtime": 1750604824421, "results": "48", "hashOfConfig": "30"}, {"size": 1623, "mtime": 1750603999427, "results": "49", "hashOfConfig": "30"}, {"size": 9640, "mtime": 1750670284290, "results": "50", "hashOfConfig": "30"}, {"size": 13910, "mtime": 1750670468418, "results": "51", "hashOfConfig": "30"}, {"size": 8259, "mtime": 1750682899137, "results": "52", "hashOfConfig": "30"}, {"size": 7805, "mtime": 1750670401273, "results": "53", "hashOfConfig": "30"}, {"size": 8369, "mtime": 1750682947678, "results": "54", "hashOfConfig": "30"}, {"size": 9062, "mtime": 1750670519249, "results": "55", "hashOfConfig": "30"}, {"size": 12872, "mtime": 1750670677901, "results": "56", "hashOfConfig": "30"}, {"size": 9693, "mtime": 1750670564593, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "3jp6v3", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\dashboard\\stats\\route.ts", ["142"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\route.ts", ["143", "144"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\summary\\route.ts", ["145"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\debts\\[id]\\route.ts", ["146", "147", "148"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\route.ts", ["149", "150"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\products\\[id]\\route.ts", ["151", "152", "153"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\debts\\page.tsx", ["154", "155", "156", "157", "158"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\page.tsx", ["159"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx", ["160", "161", "162"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\CustomerDebtSummary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtCard.tsx", ["163", "164", "165"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\DebtForm.tsx", ["166"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductCard.tsx", ["167", "168"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\ProductForm.tsx", ["169"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\CustomerDebt.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\models\\Product.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\mongodb.ts", ["170", "171", "172", "173", "174", "175", "176"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\lib\\types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\analytics\\page.tsx", ["177", "178"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\admin\\seed\\route.ts", ["179", "180"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\analytics\\route.ts", ["181"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\inventory\\insights\\route.ts", ["182", "183"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\notifications\\route.ts", ["184", "185"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\notifications\\page.tsx", ["186", "187"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\components\\NotificationCenter.tsx", ["188", "189"], [], {"ruleId": "190", "severity": 1, "message": "191", "line": 124, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 124, "endColumn": 18, "suggestions": "194"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 48, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 48, "endColumn": 18, "suggestions": "195"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 107, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 107, "endColumn": 18, "suggestions": "196"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 106, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 106, "endColumn": 18, "suggestions": "197"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 35, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 35, "endColumn": 18, "suggestions": "198"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 119, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 119, "endColumn": 18, "suggestions": "199"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 156, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 156, "endColumn": 18, "suggestions": "200"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 48, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 48, "endColumn": 18, "suggestions": "201"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 102, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 102, "endColumn": 18, "suggestions": "202"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 35, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 35, "endColumn": 18, "suggestions": "203"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 121, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 121, "endColumn": 18, "suggestions": "204"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 158, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 158, "endColumn": 18, "suggestions": "205"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 58, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 58, "endColumn": 20, "suggestions": "206"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 74, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 74, "endColumn": 20, "suggestions": "207"}, {"ruleId": "208", "severity": 1, "message": "209", "line": 84, "column": 6, "nodeType": "210", "endLine": 84, "endColumn": 55, "suggestions": "211"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 114, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 114, "endColumn": 20, "suggestions": "212"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 142, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 142, "endColumn": 20, "suggestions": "213"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 69, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 69, "endColumn": 20, "suggestions": "214"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 38, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 38, "endColumn": 20, "suggestions": "215"}, {"ruleId": "208", "severity": 1, "message": "216", "line": 46, "column": 6, "nodeType": "210", "endLine": 46, "endColumn": 49, "suggestions": "217"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 72, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 72, "endColumn": 20, "suggestions": "218"}, {"ruleId": "219", "severity": 1, "message": "220", "line": 17, "column": 12, "nodeType": "221", "messageId": "222", "endLine": 17, "endColumn": 30}, {"ruleId": "219", "severity": 1, "message": "223", "line": 18, "column": 14, "nodeType": "221", "messageId": "222", "endLine": 18, "endColumn": 28}, {"ruleId": "219", "severity": 1, "message": "220", "line": 19, "column": 21, "nodeType": "221", "messageId": "222", "endLine": 19, "endColumn": 39}, {"ruleId": "190", "severity": 1, "message": "191", "line": 111, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 111, "endColumn": 20, "suggestions": "224"}, {"ruleId": "219", "severity": 1, "message": "225", "line": 10, "column": 12, "nodeType": "221", "messageId": "222", "endLine": 10, "endColumn": 28}, {"ruleId": "219", "severity": 1, "message": "226", "line": 11, "column": 14, "nodeType": "221", "messageId": "222", "endLine": 11, "endColumn": 31}, {"ruleId": "190", "severity": 1, "message": "191", "line": 99, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 99, "endColumn": 20, "suggestions": "227"}, {"ruleId": "219", "severity": 1, "message": "228", "line": 4, "column": 7, "nodeType": "221", "messageId": "222", "endLine": 7, "endColumn": 4}, {"ruleId": "190", "severity": 1, "message": "191", "line": 52, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 52, "endColumn": 18, "suggestions": "229"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 61, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 61, "endColumn": 18, "suggestions": "230"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 70, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 70, "endColumn": 14, "suggestions": "231"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 74, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 74, "endColumn": 16, "suggestions": "232"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 78, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 78, "endColumn": 14, "suggestions": "233"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 84, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 84, "endColumn": 14, "suggestions": "234"}, {"ruleId": "208", "severity": 1, "message": "235", "line": 73, "column": 6, "nodeType": "210", "endLine": 73, "endColumn": 14, "suggestions": "236"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 85, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 85, "endColumn": 20, "suggestions": "237"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 261, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 261, "endColumn": 18, "suggestions": "238"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 295, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 295, "endColumn": 18, "suggestions": "239"}, {"ruleId": "190", "severity": 1, "message": "191", "line": 227, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 227, "endColumn": 18, "suggestions": "240"}, {"ruleId": "219", "severity": 1, "message": "241", "line": 7, "column": 27, "nodeType": "221", "messageId": "222", "endLine": 7, "endColumn": 47}, {"ruleId": "190", "severity": 1, "message": "191", "line": 244, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 244, "endColumn": 18, "suggestions": "242"}, {"ruleId": "219", "severity": 1, "message": "241", "line": 17, "column": 27, "nodeType": "221", "messageId": "222", "endLine": 17, "endColumn": 47}, {"ruleId": "190", "severity": 1, "message": "191", "line": 264, "column": 5, "nodeType": "192", "messageId": "193", "endLine": 264, "endColumn": 18, "suggestions": "243"}, {"ruleId": "219", "severity": 1, "message": "244", "line": 7, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 7, "endColumn": 16}, {"ruleId": "190", "severity": 1, "message": "191", "line": 60, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 60, "endColumn": 20, "suggestions": "245"}, {"ruleId": "219", "severity": 1, "message": "244", "line": 6, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 6, "endColumn": 16}, {"ruleId": "190", "severity": 1, "message": "191", "line": 62, "column": 7, "nodeType": "192", "messageId": "193", "endLine": 62, "endColumn": 20, "suggestions": "246"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["247"], ["248"], ["249"], ["250"], ["251"], ["252"], ["253"], ["254"], ["255"], ["256"], ["257"], ["258"], ["259"], ["260"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDebts'. Either include it or remove the dependency array.", "ArrayExpression", ["261"], ["262"], ["263"], ["264"], ["265"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["266"], ["267"], "no-unused-vars", "'debt' is defined but never used.", "Identifier", "unusedVar", "'debtId' is defined but never used.", ["268"], "'product' is defined but never used.", "'productId' is defined but never used.", ["269"], "'mongoose' is defined but never used.", ["270"], ["271"], ["272"], ["273"], ["274"], ["275"], "React Hook useEffect has a missing dependency: 'fetchAnalytics'. Either include it or remove the dependency array.", ["276"], ["277"], ["278"], ["279"], ["280"], "'request' is defined but never used.", ["281"], ["282"], "'AlertTriangle' is defined but never used.", ["283"], ["284"], {"messageId": "285", "data": "286", "fix": "287", "desc": "288"}, {"messageId": "285", "data": "289", "fix": "290", "desc": "288"}, {"messageId": "285", "data": "291", "fix": "292", "desc": "288"}, {"messageId": "285", "data": "293", "fix": "294", "desc": "288"}, {"messageId": "285", "data": "295", "fix": "296", "desc": "288"}, {"messageId": "285", "data": "297", "fix": "298", "desc": "288"}, {"messageId": "285", "data": "299", "fix": "300", "desc": "288"}, {"messageId": "285", "data": "301", "fix": "302", "desc": "288"}, {"messageId": "285", "data": "303", "fix": "304", "desc": "288"}, {"messageId": "285", "data": "305", "fix": "306", "desc": "288"}, {"messageId": "285", "data": "307", "fix": "308", "desc": "288"}, {"messageId": "285", "data": "309", "fix": "310", "desc": "288"}, {"messageId": "285", "data": "311", "fix": "312", "desc": "288"}, {"messageId": "285", "data": "313", "fix": "314", "desc": "288"}, {"desc": "315", "fix": "316"}, {"messageId": "285", "data": "317", "fix": "318", "desc": "288"}, {"messageId": "285", "data": "319", "fix": "320", "desc": "288"}, {"messageId": "285", "data": "321", "fix": "322", "desc": "288"}, {"messageId": "285", "data": "323", "fix": "324", "desc": "288"}, {"desc": "325", "fix": "326"}, {"messageId": "285", "data": "327", "fix": "328", "desc": "288"}, {"messageId": "285", "data": "329", "fix": "330", "desc": "288"}, {"messageId": "285", "data": "331", "fix": "332", "desc": "288"}, {"messageId": "285", "data": "333", "fix": "334", "desc": "335"}, {"messageId": "285", "data": "336", "fix": "337", "desc": "288"}, {"messageId": "285", "data": "338", "fix": "339", "desc": "335"}, {"messageId": "285", "data": "340", "fix": "341", "desc": "288"}, {"messageId": "285", "data": "342", "fix": "343", "desc": "335"}, {"messageId": "285", "data": "344", "fix": "345", "desc": "335"}, {"desc": "346", "fix": "347"}, {"messageId": "285", "data": "348", "fix": "349", "desc": "288"}, {"messageId": "285", "data": "350", "fix": "351", "desc": "288"}, {"messageId": "285", "data": "352", "fix": "353", "desc": "288"}, {"messageId": "285", "data": "354", "fix": "355", "desc": "288"}, {"messageId": "285", "data": "356", "fix": "357", "desc": "288"}, {"messageId": "285", "data": "358", "fix": "359", "desc": "288"}, {"messageId": "285", "data": "360", "fix": "361", "desc": "288"}, {"messageId": "285", "data": "362", "fix": "363", "desc": "288"}, "removeConsole", {"propertyName": "364"}, {"range": "365", "text": "366"}, "Remove the console.error().", {"propertyName": "364"}, {"range": "367", "text": "366"}, {"propertyName": "364"}, {"range": "368", "text": "366"}, {"propertyName": "364"}, {"range": "369", "text": "366"}, {"propertyName": "364"}, {"range": "370", "text": "366"}, {"propertyName": "364"}, {"range": "371", "text": "366"}, {"propertyName": "364"}, {"range": "372", "text": "366"}, {"propertyName": "364"}, {"range": "373", "text": "366"}, {"propertyName": "364"}, {"range": "374", "text": "366"}, {"propertyName": "364"}, {"range": "375", "text": "366"}, {"propertyName": "364"}, {"range": "376", "text": "366"}, {"propertyName": "364"}, {"range": "377", "text": "366"}, {"propertyName": "364"}, {"range": "378", "text": "366"}, {"propertyName": "364"}, {"range": "379", "text": "366"}, "Update the dependencies array to be: [viewMode, currentPage, searchTerm, filterStatus, fetchDebts]", {"range": "380", "text": "381"}, {"propertyName": "364"}, {"range": "382", "text": "366"}, {"propertyName": "364"}, {"range": "383", "text": "366"}, {"propertyName": "364"}, {"range": "384", "text": "366"}, {"propertyName": "364"}, {"range": "385", "text": "366"}, "Update the dependencies array to be: [currentPage, fetchProducts, searchTerm, selectedCategory]", {"range": "386", "text": "387"}, {"propertyName": "364"}, {"range": "388", "text": "366"}, {"propertyName": "364"}, {"range": "389", "text": "366"}, {"propertyName": "364"}, {"range": "390", "text": "366"}, {"propertyName": "391"}, {"range": "392", "text": "366"}, "Remove the console.log().", {"propertyName": "364"}, {"range": "393", "text": "366"}, {"propertyName": "391"}, {"range": "394", "text": "366"}, {"propertyName": "364"}, {"range": "395", "text": "366"}, {"propertyName": "391"}, {"range": "396", "text": "366"}, {"propertyName": "391"}, {"range": "397", "text": "366"}, "Update the dependencies array to be: [fetchAnalytics, period]", {"range": "398", "text": "399"}, {"propertyName": "364"}, {"range": "400", "text": "366"}, {"propertyName": "364"}, {"range": "401", "text": "366"}, {"propertyName": "364"}, {"range": "402", "text": "366"}, {"propertyName": "364"}, {"range": "403", "text": "366"}, {"propertyName": "364"}, {"range": "404", "text": "366"}, {"propertyName": "364"}, {"range": "405", "text": "366"}, {"propertyName": "364"}, {"range": "406", "text": "366"}, {"propertyName": "364"}, {"range": "407", "text": "366"}, "error", [3024, 3080], "", [1283, 1329], [2696, 2741], [2911, 2964], [838, 883], [2872, 2917], [3733, 3778], [1249, 1298], [2529, 2577], [844, 892], [2968, 3016], [3851, 3899], [1847, 1893], [2269, 2322], [2456, 2505], "[viewMode, currentPage, searchTerm, filterStatus, fetchDebts]", [3184, 3229], [3866, 3921], [1427, 1483], [1401, 1450], [1550, 1593], "[currentPage, fetchProducts, searchTerm, selectedCategory]", [2198, 2246], [3007, 3050], [2486, 2532], "log", [1203, 1241], [1384, 1432], [1592, 1640], [1691, 1742], [1797, 1850], [1960, 2027], [1615, 1623], "[fetchAnalytics, period]", [1913, 1963], [7239, 7287], [8055, 8107], [7525, 7575], [8076, 8135], [8773, 8827], [1409, 1463], [1523, 1577]]