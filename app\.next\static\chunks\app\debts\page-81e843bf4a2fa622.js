(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[923],{1482:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2915:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4229:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},4616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},5868:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7225:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(5594),r=s(376),l=s(5155),d=s(2115),i=s(4616),c=s(7580),n=s(9074),o=s(2895);let m=(0,o.A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var x=s(5868),u=s(7924),h=s(1482),p=s(4416);let g=(0,o.A)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var b=s(4229);function y(e){let{debt:t,onSubmit:s,onCancel:i}=e,[c,n]=(0,d.useState)({customerName:"",productName:"",productPrice:"",quantity:"",dateOfDebt:"",isPaid:!1,notes:""}),[o,m]=(0,d.useState)(!1),[x,u]=(0,d.useState)({});(0,d.useEffect)(()=>{t?n({customerName:t.customerName,productName:t.productName,productPrice:t.productPrice.toString(),quantity:t.quantity.toString(),dateOfDebt:t.dateOfDebt?new Date(t.dateOfDebt).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],isPaid:t.isPaid,notes:t.notes||""}):n(e=>(0,r._)((0,a._)({},e),{dateOfDebt:new Date().toISOString().split("T")[0]}))},[t]);let h=()=>{let e={};return c.customerName.trim()||(e.customerName="Customer name is required"),c.productName.trim()||(e.productName="Product name is required"),(!c.productPrice||0>=parseFloat(c.productPrice))&&(e.productPrice="Valid product price is required"),(!c.quantity||0>=parseInt(c.quantity))&&(e.quantity="Valid quantity is required"),c.dateOfDebt||(e.dateOfDebt="Date of debt is required"),u(e),0===Object.keys(e).length},y=async e=>{if(e.preventDefault(),h()){m(!0);try{let e=t?"/api/debts/".concat(t._id):"/api/debts",l=t?"PUT":"POST",d=await fetch(e,{method:l,headers:{"Content-Type":"application/json"},body:JSON.stringify((0,r._)((0,a._)({},c),{productPrice:parseFloat(c.productPrice),quantity:parseInt(c.quantity)}))}),i=await d.json();i.success?s():alert(i.error||"Failed to save debt record")}catch(e){alert("Error saving debt record")}finally{m(!1)}}},j=e=>{let{name:t,value:s,type:l}=e.target,d=e.target.checked;n(e=>(0,r._)((0,a._)({},e),{[t]:"checkbox"===l?d:s})),x[t]&&u(e=>(0,r._)((0,a._)({},e),{[t]:""}))};return(0,l.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4",children:(0,l.jsxs)("div",{className:"max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between border-b p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:t?"Edit Debt Record":"Add New Debt Record"}),(0,l.jsx)("button",{onClick:i,className:"text-gray-400 transition-colors hover:text-gray-600",children:(0,l.jsx)(p.A,{className:"h-6 w-6"})})]}),(0,l.jsxs)("form",{onSubmit:y,className:"space-y-4 p-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"customerName",className:"mb-1 block text-sm font-medium text-gray-700",children:"Customer Name *"}),(0,l.jsx)("input",{type:"text",id:"customerName",name:"customerName",value:c.customerName,onChange:j,className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(x.customerName?"border-red-500":"border-gray-300"),placeholder:"Enter customer name"}),x.customerName&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.customerName})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"productName",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Name *"}),(0,l.jsx)("input",{type:"text",id:"productName",name:"productName",value:c.productName,onChange:j,className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(x.productName?"border-red-500":"border-gray-300"),placeholder:"Enter product name"}),x.productName&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.productName})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"productPrice",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Price (PHP) *"}),(0,l.jsx)("input",{type:"number",id:"productPrice",name:"productPrice",value:c.productPrice,onChange:j,step:"0.01",min:"0",className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(x.productPrice?"border-red-500":"border-gray-300"),placeholder:"0.00"}),x.productPrice&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.productPrice})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"quantity",className:"mb-1 block text-sm font-medium text-gray-700",children:"Quantity *"}),(0,l.jsx)("input",{type:"number",id:"quantity",name:"quantity",value:c.quantity,onChange:j,min:"1",className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(x.quantity?"border-red-500":"border-gray-300"),placeholder:"1"}),x.quantity&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.quantity})]}),c.productPrice&&c.quantity&&(0,l.jsx)("div",{className:"rounded-lg bg-blue-50 p-3",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(g,{className:"h-5 w-5 text-blue-600"}),(0,l.jsxs)("span",{className:"text-sm font-medium text-blue-900",children:["Total Amount: ₱",((parseFloat(c.productPrice)||0)*(parseInt(c.quantity)||0)).toFixed(2)]})]})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"dateOfDebt",className:"mb-1 block text-sm font-medium text-gray-700",children:"Date of Debt *"}),(0,l.jsx)("input",{type:"date",id:"dateOfDebt",name:"dateOfDebt",value:c.dateOfDebt,onChange:j,className:"w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ".concat(x.dateOfDebt?"border-red-500":"border-gray-300")}),x.dateOfDebt&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.dateOfDebt})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",id:"isPaid",name:"isPaid",checked:c.isPaid,onChange:j,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,l.jsx)("label",{htmlFor:"isPaid",className:"ml-2 block text-sm text-gray-900",children:"Mark as paid"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"notes",className:"mb-1 block text-sm font-medium text-gray-700",children:"Notes (Optional)"}),(0,l.jsx)("textarea",{id:"notes",name:"notes",value:c.notes,onChange:j,rows:3,className:"w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",placeholder:"Additional notes about this debt..."})]}),(0,l.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,l.jsx)("button",{type:"button",onClick:i,className:"flex-1 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50",children:"Cancel"}),(0,l.jsx)("button",{type:"submit",disabled:o,className:"flex flex-1 items-center justify-center gap-2 rounded-lg bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700 disabled:opacity-50",children:o?(0,l.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-b-2 border-white"}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),t?"Update":"Create"]})})]})]})]})})}let j=(0,o.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var N=s(7108),f=s(2915),v=s(4621),w=s(2525);let k=(0,d.memo)(function(e){let{debt:t,onEdit:s,onDelete:a,onTogglePayment:r}=e,i=(0,d.useCallback)(()=>{s(t)},[s,t]),c=(0,d.useCallback)(()=>{t._id&&a(t._id)},[a,t._id]),o=(0,d.useCallback)(()=>{r(t)},[r,t]),x=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return(0,l.jsx)("div",{className:"rounded-lg border-l-4 bg-white p-6 shadow-sm ".concat(t.isPaid?"border-green-500":"border-red-500"),children:(0,l.jsxs)("div",{className:"flex flex-col justify-between gap-4 md:flex-row md:items-center",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"mb-3 flex items-center gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(j,{className:"h-5 w-5 text-gray-400"}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t.customerName})]}),(0,l.jsx)("div",{className:"rounded-full px-3 py-1 text-sm font-medium ".concat(t.isPaid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t.isPaid?"Paid":"Unpaid"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm md:grid-cols-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-gray-600",children:"Product"}),(0,l.jsx)("p",{className:"font-medium text-gray-900",children:t.productName})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-gray-600",children:"Quantity & Price"}),(0,l.jsxs)("p",{className:"font-medium text-gray-900",children:[t.quantity," \xd7 ₱",t.productPrice.toFixed(2)]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-gray-600",children:"Total Amount"}),(0,l.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:["₱",t.totalAmount.toFixed(2)]})]})]}),(0,l.jsxs)("div",{className:"mt-3 flex items-center gap-4 text-sm text-gray-600",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(n.A,{className:"h-4 w-4"}),(0,l.jsxs)("span",{children:["Debt Date: ",x(t.dateOfDebt)]})]}),(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:[(()=>{let e=new Date,s=new Date(t.dateOfDebt);return Math.ceil(Math.abs(e.getTime()-s.getTime())/864e5)})()," days ago"]}),t.isPaid&&t.paidDate&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{children:"•"}),(0,l.jsxs)("span",{children:["Paid: ",x(t.paidDate)]})]})]}),t.notes&&(0,l.jsx)("div",{className:"mt-3 rounded-lg bg-gray-50 p-3",children:(0,l.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,l.jsx)("span",{className:"font-medium",children:"Notes:"})," ",t.notes]})})]}),(0,l.jsxs)("div",{className:"flex w-full flex-col gap-2 md:w-auto",children:[(0,l.jsx)("button",{onClick:o,className:"flex items-center justify-center gap-2 rounded-lg px-4 py-2 text-sm font-medium transition-colors ".concat(t.isPaid?"bg-red-50 text-red-600 hover:bg-red-100":"bg-green-50 text-green-600 hover:bg-green-100"),children:t.isPaid?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(m,{className:"h-4 w-4"}),"Mark Unpaid"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(f.A,{className:"h-4 w-4"}),"Mark Paid"]})}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)("button",{onClick:i,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-100",children:[(0,l.jsx)(v.A,{className:"h-4 w-4"}),"Edit"]}),(0,l.jsxs)("button",{onClick:c,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-red-50 px-3 py-2 text-sm font-medium text-red-600 transition-colors hover:bg-red-100",children:[(0,l.jsx)(w.A,{className:"h-4 w-4"}),"Delete"]})]})]})]})})});var P=s(7863),D=s(6474);function A(e){var t,s;let{summary:a}=e,[r,i]=(0,d.useState)(!1),c=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),o=(null==(t=a.debts)?void 0:t.filter(e=>e.isPaid))||[],x=(null==(s=a.debts)?void 0:s.filter(e=>!e.isPaid))||[];return(0,l.jsxs)("div",{className:"overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm",children:[(0,l.jsxs)("div",{className:"cursor-pointer p-6 transition-colors hover:bg-gray-50",onClick:()=>i(!r),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(j,{className:"h-6 w-6 text-blue-600"}),(0,l.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:a.customerName})]}),a.totalUnpaid>0&&(0,l.jsxs)("div",{className:"rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-800",children:[a.unpaidCount," unpaid"]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Unpaid"}),(0,l.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:["₱",a.totalUnpaid.toFixed(2)]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Debt"}),(0,l.jsxs)("p",{className:"text-lg font-semibold text-gray-900",children:["₱",a.totalDebt.toFixed(2)]})]}),r?(0,l.jsx)(P.A,{className:"h-5 w-5 text-gray-400"}):(0,l.jsx)(D.A,{className:"h-5 w-5 text-gray-400"})]})]}),(0,l.jsxs)("div",{className:"mt-4 grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,l.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Records"}),(0,l.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:a.debtCount})]}),(0,l.jsxs)("div",{className:"rounded-lg bg-red-50 p-3 text-center",children:[(0,l.jsx)("p",{className:"text-sm text-red-600",children:"Unpaid Records"}),(0,l.jsx)("p",{className:"text-lg font-semibold text-red-900",children:a.unpaidCount})]}),(0,l.jsxs)("div",{className:"rounded-lg bg-green-50 p-3 text-center",children:[(0,l.jsx)("p",{className:"text-sm text-green-600",children:"Paid Records"}),(0,l.jsx)("p",{className:"text-lg font-semibold text-green-900",children:a.debtCount-a.unpaidCount})]}),(0,l.jsxs)("div",{className:"rounded-lg bg-blue-50 p-3 text-center",children:[(0,l.jsx)("p",{className:"text-sm text-blue-600",children:"Payment Rate"}),(0,l.jsxs)("p",{className:"text-lg font-semibold text-blue-900",children:[a.debtCount>0?Math.round((a.debtCount-a.unpaidCount)/a.debtCount*100):0,"%"]})]})]})]}),r&&a.debts&&(0,l.jsxs)("div",{className:"border-t border-gray-200",children:[x.length>0&&(0,l.jsxs)("div",{className:"bg-red-50 p-6",children:[(0,l.jsxs)("h4",{className:"mb-4 flex items-center gap-2 text-lg font-semibold text-red-900",children:[(0,l.jsx)(m,{className:"h-5 w-5"}),"Unpaid Debts (",x.length,")"]}),(0,l.jsx)("div",{className:"space-y-3",children:x.map(e=>(0,l.jsxs)("div",{className:"rounded-lg border border-red-200 bg-white p-4",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h5",{className:"font-medium text-gray-900",children:e.productName}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:[e.quantity," \xd7 ₱",e.productPrice.toFixed(2)]}),(0,l.jsxs)("p",{className:"mt-1 flex items-center gap-1 text-xs text-gray-500",children:[(0,l.jsx)(n.A,{className:"h-3 w-3"}),c(e.dateOfDebt)]})]}),(0,l.jsx)("div",{className:"text-right",children:(0,l.jsxs)("p",{className:"text-lg font-bold text-red-600",children:["₱",e.totalAmount.toFixed(2)]})})]}),e.notes&&(0,l.jsxs)("p",{className:"mt-2 text-sm italic text-gray-600",children:["“",e.notes,"”"]})]},e._id))})]}),o.length>0&&(0,l.jsxs)("div",{className:"bg-green-50 p-6",children:[(0,l.jsxs)("h4",{className:"mb-4 flex items-center gap-2 text-lg font-semibold text-green-900",children:[(0,l.jsx)(f.A,{className:"h-5 w-5"}),"Paid Debts (",o.length,")"]}),(0,l.jsx)("div",{className:"space-y-3",children:o.map(e=>(0,l.jsxs)("div",{className:"rounded-lg border border-green-200 bg-white p-4",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h5",{className:"font-medium text-gray-900",children:e.productName}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:[e.quantity," \xd7 ₱",e.productPrice.toFixed(2)]}),(0,l.jsxs)("div",{className:"mt-1 flex items-center gap-4 text-xs text-gray-500",children:[(0,l.jsxs)("span",{className:"flex items-center gap-1",children:[(0,l.jsx)(n.A,{className:"h-3 w-3"}),"Debt: ",c(e.dateOfDebt)]}),e.paidDate&&(0,l.jsxs)("span",{className:"flex items-center gap-1",children:[(0,l.jsx)(f.A,{className:"h-3 w-3"}),"Paid: ",c(e.paidDate)]})]})]}),(0,l.jsx)("div",{className:"text-right",children:(0,l.jsxs)("p",{className:"text-lg font-bold text-green-600",children:["₱",e.totalAmount.toFixed(2)]})})]}),e.notes&&(0,l.jsxs)("p",{className:"mt-2 text-sm italic text-gray-600",children:["“",e.notes,"”"]})]},e._id))})]})]})]})}var C=s(838);function S(){let[e,t]=(0,d.useState)([]),[s,o]=(0,d.useState)([]),[p,g]=(0,d.useState)(!0),[b,j]=(0,d.useState)(!1),[N,f]=(0,d.useState)(null),[v,w]=(0,d.useState)(""),[P,D]=(0,d.useState)("all"),[S,q]=(0,d.useState)("list"),[F,O]=(0,d.useState)(1),[M,_]=(0,d.useState)(1),[U,T]=(0,d.useState)({totalCustomers:0,totalDebts:0,totalUnpaidDebts:0,totalDebtAmount:0,totalUnpaidAmount:0}),E=async()=>{try{g(!0);let e=new URLSearchParams((0,a._)({page:F.toString(),limit:"10"},v&&{customer:v},"all"!==P&&{isPaid:"paid"===P?"true":"false"})),s=await fetch("/api/debts?".concat(e)),r=await s.json();r.success&&(t(r.data),_(r.pagination.pages))}catch(e){}finally{g(!1)}},R=async()=>{try{let e=await fetch("/api/debts/summary"),t=await e.json();t.success&&(o(t.data.customerSummaries),T(t.data.overallStats))}catch(e){}};(0,d.useEffect)(()=>{"list"===S?E():R()},[S,F,v,P]);let z=()=>{f(null),j(!0)},H=e=>{f(e),j(!0)},L=async e=>{if(confirm("Are you sure you want to delete this debt record?"))try{(await fetch("/api/debts/".concat(e),{method:"DELETE"})).ok?"list"===S?E():R():alert("Failed to delete debt record")}catch(e){alert("Error deleting debt record")}},I=async e=>{try{(await fetch("/api/debts/".concat(e._id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify((0,r._)((0,a._)({},e),{isPaid:!e.isPaid}))})).ok?"list"===S?E():R():alert("Failed to update payment status")}catch(e){alert("Error updating payment status")}};return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)(C.A,{}),(0,l.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,l.jsxs)("div",{className:"mb-8 flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Customer Debts (Utang)"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Track and manage customer debt records"})]}),(0,l.jsxs)("button",{onClick:z,className:"flex items-center gap-2 rounded-lg bg-green-600 px-6 py-3 text-white transition-colors hover:bg-green-700",children:[(0,l.jsx)(i.A,{className:"h-5 w-5"}),"Add Debt"]})]}),(0,l.jsxs)("div",{className:"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4",children:[(0,l.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(c.A,{className:"mr-3 h-8 w-8 text-blue-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Customers"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.totalCustomers})]})]})}),(0,l.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"mr-3 h-8 w-8 text-orange-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Debts"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.totalDebts})]})]})}),(0,l.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(m,{className:"mr-3 h-8 w-8 text-red-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Debts"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.totalUnpaidDebts})]})]})}),(0,l.jsx)("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(x.A,{className:"mr-3 h-8 w-8 text-green-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Amount"}),(0,l.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",U.totalUnpaidAmount.toFixed(2)]})]})]})})]}),(0,l.jsx)("div",{className:"mb-8 rounded-lg bg-white p-6 shadow-sm",children:(0,l.jsxs)("div",{className:"flex flex-col items-start justify-between gap-4 md:flex-row md:items-center",children:[(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>q("list"),className:"rounded-lg px-4 py-2 text-sm font-medium transition-colors ".concat("list"===S?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"Debt List"}),(0,l.jsx)("button",{onClick:()=>q("summary"),className:"rounded-lg px-4 py-2 text-sm font-medium transition-colors ".concat("summary"===S?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"Customer Summary"})]}),(0,l.jsxs)("div",{className:"flex flex-1 flex-col gap-4 md:max-w-md md:flex-row",children:[(0,l.jsx)("form",{onSubmit:e=>{e.preventDefault(),O(1),"list"===S?E():R()},className:"flex-1",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(u.A,{className:"absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400"}),(0,l.jsx)("input",{type:"text",placeholder:"Search customers...",value:v,onChange:e=>w(e.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-blue-500"})]})}),"list"===S&&(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(h.A,{className:"h-5 w-5 text-gray-400"}),(0,l.jsxs)("select",{value:P,onChange:e=>{D(e.target.value),O(1)},className:"rounded-lg border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"all",children:"All Status"}),(0,l.jsx)("option",{value:"unpaid",children:"Unpaid"}),(0,l.jsx)("option",{value:"paid",children:"Paid"})]})]})]})]})}),p?(0,l.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,l.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"})}):"list"===S?(0,l.jsx)(l.Fragment,{children:0===e.length?(0,l.jsxs)("div",{className:"py-12 text-center",children:[(0,l.jsx)(c.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,l.jsx)("h3",{className:"mb-2 text-xl font-semibold text-gray-900",children:"No debt records found"}),(0,l.jsx)("p",{className:"mb-6 text-gray-600",children:"Start tracking customer debts"}),(0,l.jsxs)("button",{onClick:z,className:"inline-flex items-center gap-2 rounded-lg bg-green-600 px-6 py-3 text-white hover:bg-green-700",children:[(0,l.jsx)(i.A,{className:"h-5 w-5"}),"Add Debt Record"]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"mb-8 grid gap-4",children:e.map(e=>(0,l.jsx)(k,{debt:e,onEdit:H,onDelete:L,onTogglePayment:I},e._id))}),M>1&&(0,l.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,l.jsx)("button",{onClick:()=>O(e=>Math.max(e-1,1)),disabled:1===F,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),Array.from({length:M},(e,t)=>t+1).map(e=>(0,l.jsx)("button",{onClick:()=>O(e),className:"rounded-lg border px-4 py-2 ".concat(F===e?"border-blue-600 bg-blue-600 text-white":"border-gray-300 hover:bg-gray-50"),children:e},e)),(0,l.jsx)("button",{onClick:()=>O(e=>Math.min(e+1,M)),disabled:F===M,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})}):(0,l.jsx)("div",{className:"grid gap-6",children:0===s.length?(0,l.jsxs)("div",{className:"py-12 text-center",children:[(0,l.jsx)(c.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,l.jsx)("h3",{className:"mb-2 text-xl font-semibold text-gray-900",children:"No customer debt records"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Customer debt summaries will appear here"})]}):s.map(e=>(0,l.jsx)(A,{summary:e},e.customerName))})]}),b&&(0,l.jsx)(y,{debt:N,onSubmit:()=>{j(!1),f(null),"list"===S?E():R()},onCancel:()=>{j(!1),f(null)}})]})}},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8334:(e,t,s)=>{Promise.resolve().then(s.bind(s,7225))}},e=>{var t=t=>e(e.s=t);e.O(0,[737,838,441,684,358],()=>t(8334)),_N_E=e.O()}]);