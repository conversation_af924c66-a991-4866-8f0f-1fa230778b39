(()=>{var e={};e.id=162,e.ids=[162],e.modules={841:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var i=a(6037),r=a.n(i);let n=new i.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(e){return Number.isInteger(e)&&e>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({customerName:1}),n.index({isPaid:1}),n.index({dateOfDebt:-1}),n.index({customerName:1,isPaid:1}),n.virtual("daysSinceDebt").get(function(){let e=new Date,t=new Date(this.dateOfDebt);return Math.ceil(Math.abs(e.getTime()-t.getTime())/864e5)}),n.pre("save",function(e){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),e()}),n.statics.getDebtSummaryByCustomer=async function(e){let t=await this.find({customerName:e}).sort({dateOfDebt:-1});return{customerName:e,totalDebt:t.reduce((e,t)=>e+t.totalAmount,0),totalUnpaid:t.filter(e=>!e.isPaid).reduce((e,t)=>e+t.totalAmount,0),debtCount:t.length,unpaidCount:t.filter(e=>!e.isPaid).length,debts:t}};let o=r().models.CustomerDebt||r().model("CustomerDebt",n)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var i=a(6037),r=a.n(i);let n=new i.Schema({name:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},image:{type:String,trim:!0,default:""},netWeight:{type:String,required:[!0,"Net weight is required"],trim:!0,maxlength:[50,"Net weight cannot exceed 50 characters"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"],validate:{validator:function(e){return e>=0},message:"Price must be a positive number"}},stockQuantity:{type:Number,required:[!0,"Stock quantity is required"],min:[0,"Stock quantity cannot be negative"],default:0},category:{type:String,required:[!0,"Category is required"],enum:{values:["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"],message:"Invalid category"}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({name:1}),n.index({category:1}),n.index({stockQuantity:1}),n.virtual("isLowStock").get(function(){return this.stockQuantity<=5}),n.pre("save",function(e){this.price<0&&(this.price=0),this.stockQuantity<0&&(this.stockQuantity=0),e()});let o=r().models.Product||r().model("Product",n)},2836:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>y});var i={};a.r(i),a.d(i,{GET:()=>l});var r=a(6559),n=a(8088),o=a(7719),s=a(2190),u=a(5745),c=a(1253),d=a(841);async function l(e){try{await (0,u.A)();let e=await c.A.aggregate([{$addFields:{stockStatus:{$switch:{branches:[{case:{$eq:["$stockQuantity",0]},then:"out_of_stock"},{case:{$lte:["$stockQuantity",2]},then:"critical"},{case:{$lte:["$stockQuantity",5]},then:"low"},{case:{$lte:["$stockQuantity",10]},then:"moderate"}],default:"good"}}}},{$group:{_id:"$stockStatus",count:{$sum:1},products:{$push:"$$ROOT"}}}]),t=await d.A.aggregate([{$match:{dateOfDebt:{$gte:new Date(Date.now()-2592e6)}}},{$group:{_id:"$productName",totalSold:{$sum:"$quantity"},totalRevenue:{$sum:"$totalAmount"},averagePrice:{$avg:"$productPrice"},salesCount:{$sum:1},lastSaleDate:{$max:"$dateOfDebt"}}},{$addFields:{dailyVelocity:{$divide:["$totalSold",30]},revenuePerDay:{$divide:["$totalRevenue",30]}}},{$sort:{dailyVelocity:-1}}]),a=await c.A.find({}).lean(),i=[];for(let e of a){let a=t.find(t=>t._id===e.name),r=a?.dailyVelocity||0,n=r>0?e.stockQuantity/r:1/0;if(n<7&&n!==1/0){let t=Math.ceil(14*r);i.push({productId:e._id,productName:e.name,currentStock:e.stockQuantity,dailyVelocity:Math.round(100*r)/100,daysUntilStockOut:Math.round(10*n)/10,recommendedOrderQuantity:t,estimatedCost:t*e.price,priority:n<3?"urgent":n<5?"high":"medium",category:e.category,price:e.price})}}i.sort((e,t)=>{let a={urgent:3,high:2,medium:1};return a[e.priority]!==a[t.priority]?a[t.priority]-a[e.priority]:e.daysUntilStockOut-t.daysUntilStockOut});let r=await c.A.aggregate([{$lookup:{from:"customerdebts",localField:"name",foreignField:"productName",as:"sales"}},{$addFields:{totalSold:{$sum:{$map:{input:"$sales",as:"sale",in:"$$sale.quantity"}}},totalRevenue:{$sum:{$map:{input:"$sales",as:"sale",in:"$$sale.totalAmount"}}}}},{$group:{_id:"$category",productCount:{$sum:1},totalStockValue:{$sum:{$multiply:["$price","$stockQuantity"]}},totalSold:{$sum:"$totalSold"},totalRevenue:{$sum:"$totalRevenue"},averagePrice:{$avg:"$price"},lowStockCount:{$sum:{$cond:[{$lte:["$stockQuantity",5]},1,0]}},outOfStockCount:{$sum:{$cond:[{$eq:["$stockQuantity",0]},1,0]}}}},{$addFields:{turnoverRate:{$cond:[{$gt:["$totalStockValue",0]},{$divide:["$totalRevenue","$totalStockValue"]},0]}}},{$sort:{totalRevenue:-1}}]),n=a.map(e=>{let a=t.find(t=>t._id===e.name),i=a?.dailyVelocity||0,r=a?.lastSaleDate?Math.floor((Date.now()-new Date(a.lastSaleDate).getTime())/864e5):999;return{_id:e._id,name:e.name,price:e.price,stockQuantity:e.stockQuantity,category:e.category,dailyVelocity:i,daysSinceLastSale:r,stockValue:e.price*e.stockQuantity}}).filter(e=>e.dailyVelocity<.1&&e.daysSinceLastSale>14&&e.stockQuantity>0).sort((e,t)=>t.stockValue-e.stockValue).slice(0,10),o=t.filter(e=>e.dailyVelocity>1).slice(0,10).map(e=>{let t=a.find(t=>t.name===e._id);return{...e,currentStock:t?.stockQuantity||0,stockValue:t?t.price*t.stockQuantity:0,category:t?.category||"unknown"}}),l=a.length,m=e.find(e=>"out_of_stock"===e._id)?.count||0,p=(e.find(e=>"critical"===e._id)?.count||0)+(e.find(e=>"low"===e._id)?.count||0),y=Math.round((l-m-p)/l*100),g={stockAnalysis:e.reduce((e,t)=>(e[t._id]={count:t.count,products:t.products.slice(0,5)},e),{}),reorderRecommendations:i,categoryPerformance:r,slowMovingItems:n,fastMovingItems:o,inventoryHealthScore:y,summary:{totalProducts:l,outOfStockCount:m,lowStockCount:p,reorderNeeded:i.length,slowMovingCount:n.length,totalInventoryValue:a.reduce((e,t)=>e+t.price*t.stockQuantity,0)},generatedAt:new Date().toISOString()};return s.NextResponse.json({success:!0,data:g})}catch(e){return s.NextResponse.json({success:!1,error:"Failed to fetch inventory insights",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let m=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/inventory/insights/route",pathname:"/api/inventory/insights",filename:"route",bundlePath:"app/api/inventory/insights/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\inventory\\insights\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:y,serverHooks:g}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:y})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var i=a(6037),r=a.n(i);let n=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;async function s(){if(o.conn)if(1===r().connection.readyState)return o.conn;else o.conn=null,o.promise=null;o.promise||(o.promise=r().connect(n,{bufferCommands:!1,maxPoolSize:10,serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3,family:4}).then(e=>e.connection));try{o.conn=await o.promise}catch(e){throw o.promise=null,Error("Failed to connect to database")}return o.conn}o||(o=global.mongoose={conn:null,promise:null}),r().connection.on("connected",()=>{}),r().connection.on("error",e=>{}),r().connection.on("disconnected",()=>{}),process.on("SIGINT",async()=>{await r().connection.close(),process.exit(0)});let u=s},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[447,580],()=>a(2836));module.exports=i})();