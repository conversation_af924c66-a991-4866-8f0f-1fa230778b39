(()=>{var e={};e.id=571,e.ids=[571],e.modules={512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return f},defaultHead:function(){return u}});let s=r(4985),a=r(740),n=r(687),i=a._(r(3210)),o=s._(r(7755)),l=r(4959),d=r(9513),c=r(4604);function u(e){void 0===e&&(e=!1);let t=[(0,n.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,n.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(148);let m=["name","httpEquiv","charSet","itemProp"];function g(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return a=>{let n=!0,i=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){i=!0;let t=a.key.slice(a.key.indexOf("$")+1);e.has(t)?n=!1:e.add(t)}switch(a.type){case"title":case"base":t.has(a.type)?n=!1:t.add(a.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(a.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?n=!1:r.add(t);else{let e=a.props[t],r=s[t]||new Set;("name"!==t||!i)&&r.has(e)?n=!1:(r.add(e),s[t]=r)}}}return n}}()).reverse().map((e,t)=>{let s=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:s})})}let f=function(e){let{children:t}=e,r=(0,i.useContext)(l.AmpStateContext),s=(0,i.useContext)(d.HeadManagerContext);return(0,n.jsx)(o.default,{reduceComponentsToState:g,headManager:s,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let s=r(4985),a=r(4953),n=r(6533),i=s._(r(1933));function o(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=n.Image},1480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:a,blurDataURL:n,objectFit:i}=e,o=s?40*s:t,l=a?40*a:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:a,quality:n}=e,i=n||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+a+"&q="+i+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4093:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),a=r(8088),n=r(8170),i=r.n(n),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8547)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4532:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(687),a=r(3210),n=r(6474),i=r(9270),o=r(8492),l=r(9080);let d=["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"];var c=r(1860),u=r(8819);function p({product:e,onSubmit:t,onCancel:r}){let[n,i]=(0,a.useState)({name:"",image:"",netWeight:"",price:"",stockQuantity:"",category:"snacks"}),[o,l]=(0,a.useState)(!1),[p,m]=(0,a.useState)({}),g=()=>{let e={};return n.name.trim()||(e.name="Product name is required"),n.netWeight.trim()||(e.netWeight="Net weight is required"),(!n.price||0>parseFloat(n.price))&&(e.price="Valid price is required"),(!n.stockQuantity||0>parseInt(n.stockQuantity))&&(e.stockQuantity="Valid stock quantity is required"),n.category||(e.category="Category is required"),m(e),0===Object.keys(e).length},f=async r=>{if(r.preventDefault(),g()){l(!0);try{let r=e?`/api/products/${e._id}`:"/api/products",s=e?"PUT":"POST",a=await fetch(r,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify({...n,price:parseFloat(n.price),stockQuantity:parseInt(n.stockQuantity)})}),i=await a.json();i.success?t():alert(i.error||"Failed to save product")}catch(e){alert("Error saving product")}finally{l(!1)}}},h=e=>{let{name:t,value:r}=e.target;i(e=>({...e,[t]:r})),p[t]&&m(e=>({...e,[t]:""}))};return(0,s.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4",children:(0,s.jsxs)("div",{className:"max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between border-b p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:e?"Edit Product":"Add New Product"}),(0,s.jsx)("button",{onClick:r,className:"text-gray-400 transition-colors hover:text-gray-600",children:(0,s.jsx)(c.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("form",{onSubmit:f,className:"space-y-4 p-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Name *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:n.name,onChange:h,className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${p.name?"border-red-500":"border-gray-300"}`,placeholder:"Enter product name"}),p.name&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:p.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"image",className:"mb-1 block text-sm font-medium text-gray-700",children:"Product Image URL"}),(0,s.jsx)("input",{type:"url",id:"image",name:"image",value:n.image,onChange:h,className:"w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",placeholder:"https://example.com/image.jpg"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"netWeight",className:"mb-1 block text-sm font-medium text-gray-700",children:"Net Weight *"}),(0,s.jsx)("input",{type:"text",id:"netWeight",name:"netWeight",value:n.netWeight,onChange:h,className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${p.netWeight?"border-red-500":"border-gray-300"}`,placeholder:"e.g., 100g, 1L, 500ml"}),p.netWeight&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:p.netWeight})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"price",className:"mb-1 block text-sm font-medium text-gray-700",children:"Price (PHP) *"}),(0,s.jsx)("input",{type:"number",id:"price",name:"price",value:n.price,onChange:h,step:"0.01",min:"0",className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${p.price?"border-red-500":"border-gray-300"}`,placeholder:"0.00"}),p.price&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:p.price})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"stockQuantity",className:"mb-1 block text-sm font-medium text-gray-700",children:"Stock Quantity *"}),(0,s.jsx)("input",{type:"number",id:"stockQuantity",name:"stockQuantity",value:n.stockQuantity,onChange:h,min:"0",className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${p.stockQuantity?"border-red-500":"border-gray-300"}`,placeholder:"0"}),p.stockQuantity&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:p.stockQuantity})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"category",className:"mb-1 block text-sm font-medium text-gray-700",children:"Category *"}),(0,s.jsx)("select",{id:"category",name:"category",value:n.category,onChange:h,className:`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 ${p.category?"border-red-500":"border-gray-300"}`,children:d.map(e=>(0,s.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),p.category&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:p.category})]}),(0,s.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,s.jsx)("button",{type:"button",onClick:r,className:"flex-1 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:o,className:"flex flex-1 items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700 disabled:opacity-50",children:o?(0,s.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-b-2 border-white"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),e?"Update":"Create"]})})]})]})]})})}var m=r(9947),g=r(9923),f=r(8233),h=r(1261),x=r.n(h);let b=(0,a.memo)(function({product:e,onEdit:t,onDelete:r}){let n=e.stockQuantity<=5,i=(0,a.useCallback)(()=>{t(e)},[t,e]),o=(0,a.useCallback)(()=>{e._id&&r(e._id)},[r,e._id]);return(0,s.jsxs)("div",{className:"overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md",children:[(0,s.jsxs)("div",{className:"relative h-48 bg-gray-100",children:[e.image?(0,s.jsx)(x(),{src:e.image,alt:e.name,fill:!0,className:"object-cover"}):(0,s.jsx)("div",{className:"flex h-full w-full items-center justify-center text-gray-400",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-lg bg-gray-200",children:"\uD83D\uDCE6"}),(0,s.jsx)("span",{className:"text-sm",children:"No Image"})]})}),n&&(0,s.jsxs)("div",{className:"absolute right-2 top-2 flex items-center gap-1 rounded-full bg-red-500 px-2 py-1 text-xs text-white",children:[(0,s.jsx)(m.A,{className:"h-3 w-3"}),"Low Stock"]})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsx)("h3",{className:"mb-1 line-clamp-2 font-semibold text-gray-900",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.netWeight})]}),(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsxs)("div",{className:"mb-1 flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",e.price.toFixed(2)]}),(0,s.jsxs)("span",{className:`rounded-full px-2 py-1 text-sm ${n?"bg-red-100 text-red-800":0===e.stockQuantity?"bg-gray-100 text-gray-800":"bg-green-100 text-green-800"}`,children:[e.stockQuantity," in stock"]})]}),(0,s.jsx)("div",{className:"text-xs capitalize text-gray-500",children:e.category})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{onClick:i,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-100",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),"Edit"]}),(0,s.jsxs)("button",{onClick:o,className:"flex flex-1 items-center justify-center gap-1 rounded-lg bg-red-50 px-3 py-2 text-sm font-medium text-red-600 transition-colors hover:bg-red-100",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),"Delete"]})]})]})]})});var y=r(2254);function v(){let[e,t]=(0,a.useState)([]),[r,c]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1),[g,f]=(0,a.useState)(null),[h,x]=(0,a.useState)(""),[v,j]=(0,a.useState)("all"),[w,N]=(0,a.useState)(1),[_,k]=(0,a.useState)(1),C=async()=>{try{c(!0);let e=new URLSearchParams({page:w.toString(),limit:"12",...h&&{search:h},..."all"!==v&&{category:v}}),r=await fetch(`/api/products?${e}`),s=await r.json();s.success&&(t(s.data),k(s.pagination.pages))}catch(e){}finally{c(!1)}},P=()=>{f(null),m(!0)},S=e=>{f(e),m(!0)},E=async e=>{if(confirm("Are you sure you want to delete this product?"))try{(await fetch(`/api/products/${e}`,{method:"DELETE"})).ok?C():alert("Failed to delete product")}catch(e){alert("Error deleting product")}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(y.A,{}),(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8 flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Product Inventory"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage your store products and inventory"})]}),(0,s.jsxs)("button",{onClick:P,className:"flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 text-white transition-colors hover:bg-blue-700",children:[(0,s.jsx)(n.A,{className:"h-5 w-5"}),"Add Product"]})]}),(0,s.jsx)("div",{className:"mb-8 rounded-lg bg-white p-6 shadow-sm",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row",children:[(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),N(1),C()},className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.A,{className:"absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search products...",value:h,onChange:e=>x(e.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-blue-500"})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-gray-400"}),(0,s.jsxs)("select",{value:v,onChange:e=>{j(e.target.value),N(1)},className:"rounded-lg border border-gray-300 px-4 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Categories"}),d.map(e=>(0,s.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]})]})]})}),r?(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsx)("div",{className:"h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"})}):0===e.length?(0,s.jsxs)("div",{className:"py-12 text-center",children:[(0,s.jsx)(l.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,s.jsx)("h3",{className:"mb-2 text-xl font-semibold text-gray-900",children:"No products found"}),(0,s.jsx)("p",{className:"mb-6 text-gray-600",children:"Get started by adding your first product"}),(0,s.jsxs)("button",{onClick:P,className:"inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 text-white hover:bg-blue-700",children:[(0,s.jsx)(n.A,{className:"h-5 w-5"}),"Add Product"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:e.map(e=>(0,s.jsx)(b,{product:e,onEdit:S,onDelete:E},e._id))}),_>1&&(0,s.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,s.jsx)("button",{onClick:()=>N(e=>Math.max(e-1,1)),disabled:1===w,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),Array.from({length:_},(e,t)=>t+1).map(e=>(0,s.jsx)("button",{onClick:()=>N(e),className:`rounded-lg border px-4 py-2 ${w===e?"border-blue-600 bg-blue-600 text-white":"border-gray-300 hover:bg-gray-50"}`,children:e},e)),(0,s.jsx)("button",{onClick:()=>N(e=>Math.min(e+1,_)),disabled:w===_,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]}),u&&(0,s.jsx)(p,{product:g,onSubmit:()=>{m(!1),f(null),C()},onCancel:()=>{m(!1),f(null)}})]})}},4604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},4953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(148);let s=r(1480),a=r(2756),n=["-moz-initial","fill","none","scale-down",void 0];function i(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,c,u,{src:p,sizes:m,unoptimized:g=!1,priority:f=!1,loading:h,className:x,quality:b,width:y,height:v,fill:j=!1,style:w,overrideSrc:N,onLoad:_,onLoadingComplete:k,placeholder:C="empty",blurDataURL:P,fetchPriority:S,decoding:E="async",layout:A,objectFit:O,objectPosition:M,lazyBoundary:z,lazyRoot:D,...R}=e,{imgConf:I,showAltText:q,blurComplete:F,defaultLoader:L}=t,T=I||a.imageConfigDefault;if("allSizes"in T)d=T;else{let e=[...T.deviceSizes,...T.imageSizes].sort((e,t)=>e-t),t=T.deviceSizes.sort((e,t)=>e-t),s=null==(r=T.qualities)?void 0:r.sort((e,t)=>e-t);d={...T,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let U=R.loader||L;delete R.loader,delete R.srcSet;let W="__next_img_default"in U;if(W){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=U;U=t=>{let{config:r,...s}=t;return e(s)}}if(A){"fill"===A&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[A];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[A];t&&!m&&(m=t)}let Q="",G=o(y),$=o(v);if((l=p)&&"object"==typeof l&&(i(l)||void 0!==l.src)){let e=i(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,P=P||e.blurDataURL,Q=e.src,!j)if(G||$){if(G&&!$){let t=G/e.width;$=Math.round(e.height*t)}else if(!G&&$){let t=$/e.height;G=Math.round(e.width*t)}}else G=e.width,$=e.height}let V=!f&&("lazy"===h||void 0===h);(!(p="string"==typeof p?p:Q)||p.startsWith("data:")||p.startsWith("blob:"))&&(g=!0,V=!1),d.unoptimized&&(g=!0),W&&!d.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(g=!0);let H=o(b),B=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:O,objectPosition:M}:{},q?{}:{color:"transparent"},w),X=F||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:G,heightInt:$,blurWidth:c,blurHeight:u,blurDataURL:P||"",objectFit:B.objectFit})+'")':'url("'+C+'")',J=n.includes(B.objectFit)?"fill"===B.objectFit?"100% 100%":"cover":B.objectFit,Z=X?{backgroundSize:J,backgroundPosition:B.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},Y=function(e){let{config:t,src:r,unoptimized:s,width:a,quality:n,sizes:i,loader:o}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:a}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:a,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))],kind:"x"}}(t,a,i),c=l.length-1;return{sizes:i||"w"!==d?i:"100vw",srcSet:l.map((e,s)=>o({config:t,src:r,quality:n,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:o({config:t,src:r,quality:n,width:l[c]})}}({config:d,src:p,unoptimized:g,width:G,quality:H,sizes:m,loader:U});return{props:{...R,loading:V?"lazy":h,fetchPriority:S,width:G,height:$,decoding:E,className:x,style:{...B,...Z},sizes:Y.sizes,srcSet:Y.srcSet,src:N||Y.src},meta:{unoptimized:g,priority:f,placeholder:C,fill:j}}}},4959:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.AmpContext},6474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return v}});let s=r(4985),a=r(740),n=r(687),i=a._(r(3210)),o=s._(r(1215)),l=s._(r(512)),d=r(4953),c=r(2756),u=r(7903);r(148);let p=r(9148),m=s._(r(1933)),g=r(3038),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function h(e,t,r,s,a,n,i){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&a(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,a=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{a=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function x(e){return i.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let b=(0,i.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:a,height:o,width:l,decoding:d,className:c,style:u,fetchPriority:p,placeholder:m,loading:f,unoptimized:b,fill:y,onLoadRef:v,onLoadingCompleteRef:j,setBlurComplete:w,setShowAltText:N,sizesInput:_,onLoad:k,onError:C,...P}=e,S=(0,i.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&h(e,m,v,j,w,b,_))},[r,m,v,j,w,C,b,_]),E=(0,g.useMergedRef)(t,S);return(0,n.jsx)("img",{...P,...x(p),loading:f,width:l,height:o,decoding:d,"data-nimg":y?"fill":"1",className:c,style:u,sizes:a,srcSet:s,src:r,ref:E,onLoad:e=>{h(e.currentTarget,m,v,j,w,b,_)},onError:e=>{N(!0),"empty"!==m&&w(!0),C&&C(e)}})});function y(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...x(r.fetchPriority)};return t&&o.default.preload?(o.default.preload(r.src,s),null):(0,n.jsx)(l.default,{children:(0,n.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...s},"__nimg-"+r.src+r.srcSet+r.sizes)})}let v=(0,i.forwardRef)((e,t)=>{let r=(0,i.useContext)(p.RouterContext),s=(0,i.useContext)(u.ImageConfigContext),a=(0,i.useMemo)(()=>{var e;let t=f||s||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),a=t.deviceSizes.sort((e,t)=>e-t),n=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:a,qualities:n}},[s]),{onLoad:o,onLoadingComplete:l}=e,g=(0,i.useRef)(o);(0,i.useEffect)(()=>{g.current=o},[o]);let h=(0,i.useRef)(l);(0,i.useEffect)(()=>{h.current=l},[l]);let[x,v]=(0,i.useState)(!1),[j,w]=(0,i.useState)(!1),{props:N,meta:_}=(0,d.getImgProps)(e,{defaultLoader:m.default,imgConf:a,blurComplete:x,showAltText:j});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(b,{...N,unoptimized:_.unoptimized,placeholder:_.placeholder,fill:_.fill,onLoadRef:g,onLoadingCompleteRef:h,setBlurComplete:v,setShowAltText:w,sizesInput:e.sizes,ref:t}),_.priority?(0,n.jsx)(y,{isAppRouter:!r,imgAttributes:N}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let s=r(3210),a=()=>{},n=()=>{};function i(e){var t;let{headManager:r,reduceComponentsToState:i}=e;function o(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(i(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),a(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),a(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),n(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},7902:(e,t,r)=>{Promise.resolve().then(r.bind(r,4532))},7903:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.ImageConfigContext},8166:(e,t,r)=>{Promise.resolve().then(r.bind(r,8547))},8233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8492:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},8547:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\tindahan\\\\app\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\products\\page.tsx","default")},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.RouterContext},9270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.HeadManagerContext},9923:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},9947:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,945,73,188],()=>r(4093));module.exports=s})();