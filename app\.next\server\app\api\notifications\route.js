(()=>{var e={};e.id=170,e.ids=[170],e.modules={695:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var a={};i.r(a),i.d(a,{GET:()=>m});var r=i(6559),n=i(8088),o=i(7719),s=i(2190),u=i(5745),d=i(1253),c=i(841);async function m(e){try{await (0,u.A)();let e=[];(await d.A.find({stockQuantity:{$lte:5,$gt:0}}).lean()).forEach(t=>{e.push({id:`low_stock_${t._id}`,type:"low_stock",title:"Low Stock Alert",message:`${t.name} is running low (${t.stockQuantity} left)`,severity:t.stockQuantity<=2?"high":"medium",data:{productId:t._id,productName:t.name,currentStock:t.stockQuantity,category:t.category,price:t.price},createdAt:new Date})}),(await d.A.find({stockQuantity:0}).lean()).forEach(t=>{e.push({id:`out_of_stock_${t._id}`,type:"out_of_stock",title:"Out of Stock",message:`${t.name} is out of stock`,severity:"critical",data:{productId:t._id,productName:t.name,category:t.category,price:t.price},createdAt:new Date})});let t=new Date;t.setDate(t.getDate()-7);let i=(await c.A.find({isPaid:!1,dateOfDebt:{$lt:t}}).lean()).reduce((e,t)=>(e[t.customerName]||(e[t.customerName]={debts:[],totalAmount:0,oldestDebt:t.dateOfDebt}),e[t.customerName].debts.push(t),e[t.customerName].totalAmount+=t.totalAmount,t.dateOfDebt<e[t.customerName].oldestDebt&&(e[t.customerName].oldestDebt=t.dateOfDebt),e),{});Object.entries(i).forEach(([t,i])=>{let a=Math.floor((Date.now()-new Date(i.oldestDebt).getTime())/864e5);e.push({id:`overdue_debt_${t.replace(/\s+/g,"_")}`,type:"overdue_debt",title:"Overdue Debt",message:`${t} has ₱${i.totalAmount.toFixed(2)} in overdue debts (${a} days)`,severity:a>14?"critical":a>7?"high":"medium",data:{customerName:t,totalAmount:i.totalAmount,debtCount:i.debts.length,daysOverdue:a,oldestDebt:i.oldestDebt,debts:i.debts},createdAt:new Date})}),(await c.A.aggregate([{$group:{_id:"$customerName",totalDebts:{$sum:1},totalAmount:{$sum:"$totalAmount"},unpaidAmount:{$sum:{$cond:[{$eq:["$isPaid",!1]},"$totalAmount",0]}},unpaidCount:{$sum:{$cond:[{$eq:["$isPaid",!1]},1,0]}},paymentRate:{$avg:{$cond:[{$eq:["$isPaid",!0]},1,0]}}}},{$match:{$or:[{unpaidAmount:{$gt:200}},{paymentRate:{$lt:.5}},{unpaidCount:{$gt:3}}]}}])).forEach(t=>{let i=[],a="medium";t.unpaidAmount>500?(i.push("Very high unpaid amount"),a="critical"):t.unpaidAmount>200&&(i.push("High unpaid amount"),a="high"),t.paymentRate<.3?(i.push("Very low payment rate"),a="critical"):t.paymentRate<.5&&(i.push("Low payment rate"),"critical"!==a&&(a="high")),t.unpaidCount>5?(i.push("Many unpaid debts"),a="critical"):t.unpaidCount>3&&(i.push("Multiple unpaid debts"),"critical"!==a&&(a="high")),e.push({id:`high_risk_${t._id.replace(/\s+/g,"_")}`,type:"high_risk_customer",title:"High Risk Customer",message:`${t._id} is a high-risk customer (₱${t.unpaidAmount.toFixed(2)} unpaid)`,severity:a,data:{customerName:t._id,unpaidAmount:t.unpaidAmount,unpaidCount:t.unpaidCount,paymentRate:Math.round(100*t.paymentRate),riskFactors:i},createdAt:new Date})});let a=new Date;a.setDate(a.getDate()-6);let r=new Date;r.setDate(r.getDate()-3);let n=(await c.A.find({isPaid:!1,dateOfDebt:{$gte:a,$lte:r}}).lean()).reduce((e,t)=>(e[t.customerName]||(e[t.customerName]={debts:[],totalAmount:0}),e[t.customerName].debts.push(t),e[t.customerName].totalAmount+=t.totalAmount,e),{});Object.entries(n).forEach(([t,i])=>{e.push({id:`payment_reminder_${t.replace(/\s+/g,"_")}`,type:"payment_reminder",title:"Payment Reminder",message:`Remind ${t} about ₱${i.totalAmount.toFixed(2)} in pending payments`,severity:"low",data:{customerName:t,totalAmount:i.totalAmount,debtCount:i.debts.length,debts:i.debts},createdAt:new Date})});let o={critical:4,high:3,medium:2,low:1};e.sort((e,t)=>o[e.severity]!==o[t.severity]?o[t.severity]-o[e.severity]:new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime());let m={total:e.length,critical:e.filter(e=>"critical"===e.severity).length,high:e.filter(e=>"high"===e.severity).length,medium:e.filter(e=>"medium"===e.severity).length,low:e.filter(e=>"low"===e.severity).length,byType:{low_stock:e.filter(e=>"low_stock"===e.type).length,out_of_stock:e.filter(e=>"out_of_stock"===e.type).length,overdue_debt:e.filter(e=>"overdue_debt"===e.type).length,high_risk_customer:e.filter(e=>"high_risk_customer"===e.type).length,payment_reminder:e.filter(e=>"payment_reminder"===e.type).length}};return s.NextResponse.json({success:!0,data:{notifications:e,summary:m,generatedAt:new Date().toISOString()}})}catch(e){return s.NextResponse.json({success:!1,error:"Failed to fetch notifications",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new r.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/notifications/route",pathname:"/api/notifications",filename:"route",bundlePath:"app/api/notifications/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\app\\src\\app\\api\\notifications\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:g}=l;function y(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},841:(e,t,i)=>{"use strict";i.d(t,{A:()=>o});var a=i(6037),r=i.n(a);let n=new a.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Customer name cannot exceed 100 characters"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},productPrice:{type:Number,required:[!0,"Product price is required"],min:[0,"Product price cannot be negative"]},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],validate:{validator:function(e){return Number.isInteger(e)&&e>0},message:"Quantity must be a positive integer"}},totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},dateOfDebt:{type:Date,required:[!0,"Date of debt is required"],default:Date.now},isPaid:{type:Boolean,default:!1},paidDate:{type:Date,default:null},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"],default:""}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({customerName:1}),n.index({isPaid:1}),n.index({dateOfDebt:-1}),n.index({customerName:1,isPaid:1}),n.virtual("daysSinceDebt").get(function(){let e=new Date,t=new Date(this.dateOfDebt);return Math.ceil(Math.abs(e.getTime()-t.getTime())/864e5)}),n.pre("save",function(e){this.totalAmount=this.productPrice*this.quantity,this.isPaid&&!this.paidDate&&(this.paidDate=new Date),!this.isPaid&&this.paidDate&&(this.paidDate=void 0),e()}),n.statics.getDebtSummaryByCustomer=async function(e){let t=await this.find({customerName:e}).sort({dateOfDebt:-1});return{customerName:e,totalDebt:t.reduce((e,t)=>e+t.totalAmount,0),totalUnpaid:t.filter(e=>!e.isPaid).reduce((e,t)=>e+t.totalAmount,0),debtCount:t.length,unpaidCount:t.filter(e=>!e.isPaid).length,debts:t}};let o=r().models.CustomerDebt||r().model("CustomerDebt",n)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1253:(e,t,i)=>{"use strict";i.d(t,{A:()=>o});var a=i(6037),r=i.n(a);let n=new a.Schema({name:{type:String,required:[!0,"Product name is required"],trim:!0,maxlength:[100,"Product name cannot exceed 100 characters"]},image:{type:String,trim:!0,default:""},netWeight:{type:String,required:[!0,"Net weight is required"],trim:!0,maxlength:[50,"Net weight cannot exceed 50 characters"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"],validate:{validator:function(e){return e>=0},message:"Price must be a positive number"}},stockQuantity:{type:Number,required:[!0,"Stock quantity is required"],min:[0,"Stock quantity cannot be negative"],default:0},category:{type:String,required:[!0,"Category is required"],enum:{values:["snacks","canned goods","beverages","personal care","household","condiments","instant foods","dairy","frozen","others"],message:"Invalid category"}}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({name:1}),n.index({category:1}),n.index({stockQuantity:1}),n.virtual("isLowStock").get(function(){return this.stockQuantity<=5}),n.pre("save",function(e){this.price<0&&(this.price=0),this.stockQuantity<0&&(this.stockQuantity=0),e()});let o=r().models.Product||r().model("Product",n)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5745:(e,t,i)=>{"use strict";i.d(t,{A:()=>u});var a=i(6037),r=i.n(a);let n=process.env.MONGODB_URI||"mongodb://localhost:27017/sari-sari-store";if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;async function s(){if(o.conn)if(1===r().connection.readyState)return o.conn;else o.conn=null,o.promise=null;o.promise||(o.promise=r().connect(n,{bufferCommands:!1,maxPoolSize:10,serverSelectionTimeoutMS:5e3,socketTimeoutMS:45e3,family:4}).then(e=>e.connection));try{o.conn=await o.promise}catch(e){throw o.promise=null,Error("Failed to connect to database")}return o.conn}o||(o=global.mongoose={conn:null,promise:null}),r().connection.on("connected",()=>{}),r().connection.on("error",e=>{}),r().connection.on("disconnected",()=>{}),process.on("SIGINT",async()=>{await r().connection.close(),process.exit(0)});let u=s},6037:e=>{"use strict";e.exports=require("mongoose")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[447,580],()=>i(695));module.exports=a})();