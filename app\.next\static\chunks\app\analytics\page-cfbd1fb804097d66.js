(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[745],{809:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3564:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(5155),l=t(2115),r=t(838),d=t(7624),c=t(3904),i=t(5868),n=t(809),x=t(7108);let m=(0,t(2895).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var o=t(3109),h=t(4576),u=t(9074),g=t(7580);function j(){let[e,s]=(0,l.useState)(null),[t,j]=(0,l.useState)(!0),[p,N]=(0,l.useState)("30");(0,l.useEffect)(()=>{y()},[p]);let y=async()=>{j(!0);try{let e=await fetch("/api/analytics?period=".concat(p)),t=await e.json();t.success&&s(t.data)}catch(e){}finally{j(!1)}},v=e=>{switch(e){case"high":return"text-red-600 bg-red-50 border-red-200";case"medium":return"text-orange-600 bg-orange-50 border-orange-200";default:return"text-yellow-600 bg-yellow-50 border-yellow-200"}};return t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(r.A,{}),(0,a.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"text-lg text-gray-600",children:"Loading analytics..."})]})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(r.A,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Business Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Advanced insights and performance metrics"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-4 sm:mt-0",children:[(0,a.jsxs)("select",{value:p,onChange:e=>N(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"7",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90",children:"Last 90 days"}),(0,a.jsx)("option",{value:"365",children:"Last year"})]}),(0,a.jsxs)("button",{onClick:y,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Refresh"})]})]})]}),e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"h-8 w-8 text-green-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.totalRevenue.toFixed(2)]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-red-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Unpaid Amount"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.totalUnpaidAmount.toFixed(2)]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Inventory Value"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.totalInventoryValue.toFixed(2)]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m,{className:"h-8 w-8 text-purple-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg Debt"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₱",e.businessSummary.averageDebtAmount.toFixed(2)]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-orange-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Payment Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.businessSummary.paymentRate.toFixed(1),"%"]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(h.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Inventory by Category"})]}),(0,a.jsx)("div",{className:"space-y-4",children:e.inventoryAnalytics.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 capitalize",children:e.category}),(0,a.jsxs)("span",{className:"text-sm font-bold text-blue-600",children:["₱",e.totalStockValue.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:["Products: ",e.totalProducts]}),(0,a.jsxs)("div",{children:["Stock: ",e.totalStock]}),(0,a.jsxs)("div",{children:["Avg Price: ₱",e.averagePrice.toFixed(2)]}),(0,a.jsxs)("div",{className:"text-red-600",children:["Low Stock: ",e.lowStockCount]})]})]},e.category))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 text-green-600 mr-2"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Top Products"})]}),(0,a.jsx)("div",{className:"space-y-3",children:e.productPerformance.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3",children:s+1}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.productName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.totalSold," sold • ",e.uniqueCustomers," customers"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-bold text-green-600",children:["₱",e.totalRevenue.toFixed(2)]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["₱",e.averagePrice.toFixed(2)," avg"]})]})]},e.productName))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(u.A,{className:"h-6 w-6 text-red-600 mr-2"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Overdue Debts"})]}),e.overdueDebts.length>0?(0,a.jsx)("div",{className:"space-y-3",children:e.overdueDebts.slice(0,5).map(e=>(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.customerName}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.productName})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-bold text-red-600",children:["₱",e.totalAmount.toFixed(2)]}),(0,a.jsxs)("p",{className:"text-sm text-red-500",children:[e.daysOverdue," days overdue"]})]})]})},"".concat(e.customerName,"-").concat(e.productName)))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No overdue debts"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(g.A,{className:"h-6 w-6 text-orange-600 mr-2"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Risk Customers"})]}),e.riskCustomers.length>0?(0,a.jsx)("div",{className:"space-y-3",children:e.riskCustomers.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg ".concat(v(e.riskLevel)),children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("p",{className:"font-medium",children:e.customerName}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-bold",children:["₱",e.unpaidAmount.toFixed(2)]}),(0,a.jsxs)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("high"===e.riskLevel?"bg-red-100 text-red-800":"medium"===e.riskLevel?"bg-orange-100 text-orange-800":"bg-yellow-100 text-yellow-800"),children:[e.riskLevel.toUpperCase()," RISK"]})]})]}),(0,a.jsx)("div",{className:"text-sm",children:e.riskFactors.map((e,s)=>(0,a.jsxs)("span",{className:"inline-block mr-2 mb-1",children:["• ",e]},s))})]},e.customerName))}):(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No high-risk customers"})]})]})]})]})]})}},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},5868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7624:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},9536:(e,s,t)=>{Promise.resolve().then(t.bind(t,3564))}},e=>{var s=s=>e(e.s=s);e.O(0,[737,838,441,684,358],()=>s(9536)),_N_E=e.O()}]);